from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
from cart.models import Cart, CartItem
from catalogue.models import Service
from .models import Order, OrderItem, OrderStatusHistory, OrderCancellation, OrderReschedule
from .serializers import (
    OrderSerializer, OrderListSerializer, CreateOrderSerializer,
    OrderStatusUpdateSerializer, OrderCancellationSerializer,
    OrderRescheduleSerializer, OrderStatusHistorySerializer,
    AssignProviderSerializer, OrderPaymentUpdateSerializer,
    CustomerOrderSerializer, ProviderOrderSerializer
)


class OrderListCreateView(generics.ListCreateAPIView):
    """
    List orders or create new order from cart.
    """
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CreateOrderSerializer
        return OrderListSerializer
    
    def get_queryset(self):
        user = self.request.user
        if user.user_type == 'staff':
            return Order.objects.all().select_related('customer', 'assigned_provider')
        elif user.user_type == 'provider':
            return Order.objects.filter(assigned_provider=user).select_related('customer')
        else:  # customer
            return Order.objects.filter(customer=user)
    
    def create(self, request, *args, **kwargs):
        """Create order from cart"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        cart_id = serializer.validated_data['cart_id']
        delivery_address = serializer.validated_data['delivery_address']
        payment_method = serializer.validated_data['payment_method']
        
        try:
            # Get user's cart
            cart = Cart.objects.get(
                id=cart_id,
                user=request.user,
                is_active=True
            )
            
            if not cart.items.exists():
                return Response({
                    'success': False,
                    'message': 'Cart is empty'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            with transaction.atomic():
                # Create order
                order = Order.objects.create(
                    customer=request.user,
                    subtotal=cart.sub_total,
                    tax_amount=cart.tax_amount,
                    discount_amount=cart.discount_amount,
                    minimum_order_fee=cart.minimum_order_fee_applied,
                    total_amount=cart.total_amount,
                    coupon_code=cart.coupon_code_applied,
                    coupon_discount=cart.discount_amount,
                    delivery_address=delivery_address,
                    payment_method=payment_method,
                    scheduled_date=serializer.validated_data.get('scheduled_date'),
                    scheduled_time_slot=serializer.validated_data.get('scheduled_time_slot'),
                    customer_notes=serializer.validated_data.get('customer_notes', '')
                )
                
                # Create order items from cart items
                for cart_item in cart.items.all():
                    # Get the service object from catalogue database
                    try:
                        service = Service.objects.using('catalogue_db').get(id=cart_item.service_id)

                        OrderItem.objects.create(
                            order=order,
                            service=service,  # Use the service object for ForeignKey
                            quantity=cart_item.quantity,
                            unit_price=cart_item.price_at_add,
                            discount_per_unit=cart_item.discount_at_add,
                            total_price=cart_item.get_total_price(),
                            estimated_duration=service.time_to_complete if hasattr(service, 'time_to_complete') else None
                        )
                    except Service.DoesNotExist:
                        # If service doesn't exist, skip this item or handle gracefully
                        continue
                
                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    new_status='pending',
                    changed_by=request.user,
                    reason='Order created'
                )
                
                # Mark cart as inactive
                cart.is_active = False
                cart.save()
                
                return Response({
                    'success': True,
                    'message': 'Order created successfully',
                    'order': OrderSerializer(order).data
                }, status=status.HTTP_201_CREATED)
                
        except Cart.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Cart not found'
            }, status=status.HTTP_404_NOT_FOUND)


class OrderDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete an order.
    """
    permission_classes = [IsAuthenticated]
    lookup_field = 'order_number'
    
    def get_serializer_class(self):
        user = self.request.user
        if user.user_type == 'staff':
            return OrderSerializer
        elif user.user_type == 'provider':
            return ProviderOrderSerializer
        else:  # customer
            return CustomerOrderSerializer
    
    def get_queryset(self):
        user = self.request.user
        if user.user_type == 'staff':
            return Order.objects.all()
        elif user.user_type == 'provider':
            return Order.objects.filter(assigned_provider=user)
        else:  # customer
            return Order.objects.filter(customer=user)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_order_status(request, order_number):
    """
    Update order status (Admin and Provider only).
    """
    if request.user.user_type not in ['staff', 'provider']:
        return Response({
            'success': False,
            'message': 'Permission denied'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        if request.user.user_type == 'staff':
            order = Order.objects.get(order_number=order_number)
        else:  # provider
            order = Order.objects.get(order_number=order_number, assigned_provider=request.user)
        
        serializer = OrderStatusUpdateSerializer(data=request.data)
        if serializer.is_valid():
            previous_status = order.status
            new_status = serializer.validated_data['status']
            reason = serializer.validated_data.get('reason', '')
            admin_notes = serializer.validated_data.get('admin_notes', '')
            
            # Update order
            order.status = new_status
            if admin_notes:
                order.admin_notes = admin_notes
            
            # Set timestamps based on status
            if new_status == 'confirmed' and not order.confirmed_at:
                order.confirmed_at = timezone.now()
            elif new_status == 'completed' and not order.completed_at:
                order.completed_at = timezone.now()
            elif new_status == 'cancelled' and not order.cancelled_at:
                order.cancelled_at = timezone.now()
            
            order.save()
            
            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=previous_status,
                new_status=new_status,
                changed_by=request.user,
                reason=reason
            )
            
            return Response({
                'success': True,
                'message': f'Order status updated to {new_status}',
                'order': OrderSerializer(order).data
            })
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cancel_order(request, order_number):
    """
    Cancel an order.
    """
    try:
        if request.user.user_type == 'staff':
            order = Order.objects.get(order_number=order_number)
        else:  # customer
            order = Order.objects.get(order_number=order_number, customer=request.user)
        
        if not order.can_be_cancelled():
            return Response({
                'success': False,
                'message': 'Order cannot be cancelled at this stage'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        reason = request.data.get('reason', 'customer_request')
        description = request.data.get('description', '')
        
        with transaction.atomic():
            # Update order status
            order.status = 'cancelled'
            order.cancelled_at = timezone.now()
            order.save()
            
            # Create cancellation record
            OrderCancellation.objects.create(
                order=order,
                reason=reason,
                description=description,
                cancelled_by=request.user
            )
            
            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=order.status,
                new_status='cancelled',
                changed_by=request.user,
                reason=f'Order cancelled: {reason}'
            )
        
        return Response({
            'success': True,
            'message': 'Order cancelled successfully',
            'order': OrderSerializer(order).data
        })
        
    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def assign_provider(request, order_number):
    """
    Assign service provider to order (Admin only).
    """
    try:
        order = Order.objects.get(order_number=order_number)
        
        serializer = AssignProviderSerializer(data=request.data)
        if serializer.is_valid():
            provider_id = serializer.validated_data['provider_id']
            notes = serializer.validated_data.get('notes', '')
            
            from authentication.models import User
            provider = User.objects.get(id=provider_id, user_type='provider')
            
            # Update order
            order.assigned_provider = provider
            order.status = 'assigned'
            if notes:
                order.admin_notes = notes
            order.save()
            
            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                previous_status='confirmed',
                new_status='assigned',
                changed_by=request.user,
                reason=f'Assigned to provider: {provider.get_full_name()}'
            )
            
            return Response({
                'success': True,
                'message': f'Order assigned to {provider.get_full_name()}',
                'order': OrderSerializer(order).data
            })
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except User.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Provider not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def reschedule_order(request, order_number):
    """
    Request order rescheduling.
    """
    try:
        if request.user.user_type == 'staff':
            order = Order.objects.get(order_number=order_number)
        else:  # customer
            order = Order.objects.get(order_number=order_number, customer=request.user)

        if not order.can_be_rescheduled():
            return Response({
                'success': False,
                'message': 'Order cannot be rescheduled at this stage'
            }, status=status.HTTP_400_BAD_REQUEST)

        new_date = request.data.get('new_date')
        new_time_slot = request.data.get('new_time_slot')
        reason = request.data.get('reason', '')

        if not new_date or not new_time_slot:
            return Response({
                'success': False,
                'message': 'New date and time slot are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create reschedule request
        reschedule = OrderReschedule.objects.create(
            order=order,
            original_date=order.scheduled_date,
            original_time_slot=order.scheduled_time_slot,
            new_date=new_date,
            new_time_slot=new_time_slot,
            reason=reason,
            requested_by=request.user,
            approved=request.user.user_type == 'staff'  # Auto-approve for staff
        )

        if request.user.user_type == 'staff':
            # Update order immediately for staff
            order.scheduled_date = new_date
            order.scheduled_time_slot = new_time_slot
            order.save()
            reschedule.approved_by = request.user
            reschedule.save()

        return Response({
            'success': True,
            'message': 'Reschedule request created successfully',
            'reschedule': OrderRescheduleSerializer(reschedule).data
        })

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_payment_status(request, order_number):
    """
    Update payment status (usually called by payment gateway).
    """
    try:
        order = Order.objects.get(order_number=order_number)

        # Only allow customer or staff to update payment
        if request.user.user_type not in ['staff'] and order.customer != request.user:
            return Response({
                'success': False,
                'message': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = OrderPaymentUpdateSerializer(data=request.data)
        if serializer.is_valid():
            order.payment_id = serializer.validated_data['payment_id']
            order.payment_signature = serializer.validated_data.get('payment_signature', '')
            order.payment_status = serializer.validated_data['payment_status']

            # Update order status based on payment
            if order.payment_status == 'paid' and order.status == 'pending':
                order.status = 'confirmed'
                order.confirmed_at = timezone.now()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    previous_status='pending',
                    new_status='confirmed',
                    changed_by=request.user,
                    reason='Payment confirmed'
                )

            order.save()

            return Response({
                'success': True,
                'message': 'Payment status updated successfully',
                'order': OrderSerializer(order).data
            })

        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


class OrderStatusHistoryView(generics.ListAPIView):
    """
    Get order status history.
    """
    serializer_class = OrderStatusHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        order_number = self.kwargs['order_number']

        # Check permissions
        if self.request.user.user_type == 'staff':
            order = get_object_or_404(Order, order_number=order_number)
        elif self.request.user.user_type == 'provider':
            order = get_object_or_404(Order, order_number=order_number, assigned_provider=self.request.user)
        else:  # customer
            order = get_object_or_404(Order, order_number=order_number, customer=self.request.user)

        return order.status_history.all()


class OrderRescheduleListView(generics.ListAPIView):
    """
    List reschedule requests for an order.
    """
    serializer_class = OrderRescheduleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        order_number = self.kwargs['order_number']

        # Check permissions
        if self.request.user.user_type == 'staff':
            order = get_object_or_404(Order, order_number=order_number)
        else:  # customer
            order = get_object_or_404(Order, order_number=order_number, customer=self.request.user)

        return order.reschedules.all()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def order_dashboard(request):
    """
    Get order dashboard statistics.
    """
    user = request.user

    if user.user_type == 'staff':
        # Admin dashboard
        total_orders = Order.objects.count()
        pending_orders = Order.objects.filter(status='pending').count()
        confirmed_orders = Order.objects.filter(status='confirmed').count()
        in_progress_orders = Order.objects.filter(status='in_progress').count()
        completed_orders = Order.objects.filter(status='completed').count()
        cancelled_orders = Order.objects.filter(status='cancelled').count()

        return Response({
            'total_orders': total_orders,
            'pending_orders': pending_orders,
            'confirmed_orders': confirmed_orders,
            'in_progress_orders': in_progress_orders,
            'completed_orders': completed_orders,
            'cancelled_orders': cancelled_orders,
            'recent_orders': OrderListSerializer(
                Order.objects.all()[:10], many=True
            ).data
        })

    elif user.user_type == 'provider':
        # Provider dashboard
        assigned_orders = Order.objects.filter(assigned_provider=user)
        pending_orders = assigned_orders.filter(status='assigned').count()
        in_progress_orders = assigned_orders.filter(status='in_progress').count()
        completed_orders = assigned_orders.filter(status='completed').count()

        return Response({
            'assigned_orders': assigned_orders.count(),
            'pending_orders': pending_orders,
            'in_progress_orders': in_progress_orders,
            'completed_orders': completed_orders,
            'recent_orders': ProviderOrderSerializer(
                assigned_orders[:10], many=True
            ).data
        })

    else:  # customer
        # Customer dashboard
        user_orders = Order.objects.filter(customer=user)
        pending_orders = user_orders.filter(status__in=['pending', 'confirmed']).count()
        in_progress_orders = user_orders.filter(status__in=['assigned', 'in_progress']).count()
        completed_orders = user_orders.filter(status='completed').count()

        return Response({
            'total_orders': user_orders.count(),
            'pending_orders': pending_orders,
            'in_progress_orders': in_progress_orders,
            'completed_orders': completed_orders,
            'recent_orders': CustomerOrderSerializer(
                user_orders[:5], many=True
            ).data
        })
