from rest_framework import serializers
from .models import Order, OrderItem, OrderStatusHistory, OrderCancellation, OrderReschedule


class OrderItemSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderItem with service details.
    """
    # service_title is now a direct field, no need to source from service
    # For additional service details, we can fetch from catalogue if needed
    service_details = serializers.SerializerMethodField()

    class Meta:
        model = OrderItem
        fields = [
            'id', 'service_id', 'service_title', 'quantity',
            'unit_price', 'discount_per_unit', 'total_price',
            'estimated_duration', 'special_instructions', 'service_details'
        ]

    def get_service_details(self, obj):
        """Get additional service details from catalogue database if needed"""
        try:
            from catalogue.models import Service
            service = Service.objects.using('catalogue_db').get(id=obj.service_id)
            return {
                'slug': service.slug,
                'image': service.image.url if service.image else None,
                'category': service.category.name if hasattr(service, 'category') and service.category else None,
                'description': getattr(service, 'description', ''),
            }
        except Service.DoesNotExist:
            return {
                'slug': None,
                'image': None,
                'category': None,
                'description': '',
            }
        except Exception:
            return {
                'slug': None,
                'image': None,
                'category': None,
                'description': '',
            }


class OrderSerializer(serializers.ModelSerializer):
    """
    Comprehensive serializer for Order with all related data including separate GST breakdown.
    """
    items = OrderItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    customer_mobile = serializers.CharField(source='customer.mobile', read_only=True)
    assigned_provider_name = serializers.CharField(
        source='assigned_provider.get_full_name',
        read_only=True
    )
    assigned_provider_mobile = serializers.CharField(
        source='assigned_provider.mobile',
        read_only=True
    )
    items_count = serializers.SerializerMethodField()
    can_cancel = serializers.ReadOnlyField(source='can_be_cancelled')
    can_reschedule = serializers.ReadOnlyField(source='can_be_rescheduled')

    # Separate tax breakdown fields
    cgst_amount = serializers.SerializerMethodField()
    sgst_amount = serializers.SerializerMethodField()
    igst_amount = serializers.SerializerMethodField()
    ugst_amount = serializers.SerializerMethodField()
    service_charge = serializers.SerializerMethodField()
    tax_breakdown = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer', 'customer_name', 'customer_mobile',
            'status', 'payment_status', 'payment_method', 'subtotal', 'tax_amount',
            'discount_amount', 'minimum_order_fee', 'total_amount', 'coupon_code',
            'coupon_discount', 'delivery_address', 'assigned_provider',
            'assigned_provider_name', 'assigned_provider_mobile', 'scheduled_date',
            'scheduled_time_slot', 'payment_id', 'customer_notes', 'admin_notes',
            'cgst_amount', 'sgst_amount', 'igst_amount', 'ugst_amount', 'service_charge',
            'tax_breakdown', 'items', 'items_count', 'can_cancel', 'can_reschedule',
            'created_at', 'updated_at', 'confirmed_at', 'completed_at', 'cancelled_at'
        ]
        read_only_fields = [
            'id', 'order_number', 'created_at', 'updated_at',
            'confirmed_at', 'completed_at', 'cancelled_at'
        ]

    def get_items_count(self, obj):
        return obj.items.count()

    def get_tax_calculation(self, obj):
        """Get detailed tax calculation for this order"""
        try:
            from taxation.models import TaxCalculation

            # Get the latest tax calculation for this order
            tax_calc = TaxCalculation.objects.filter(
                reference_type='order',
                reference_id=obj.id
            ).first()

            if tax_calc:
                return {
                    'cgst': tax_calc.cgst_amount,
                    'sgst': tax_calc.sgst_amount,
                    'igst': tax_calc.igst_amount,
                    'ugst': tax_calc.ugst_amount,
                    'service_charge': tax_calc.service_charge,
                    'total_gst': tax_calc.total_tax
                }
            else:
                # Fallback to current calculation if no stored calculation
                from taxation.services import TaxCalculationService
                return TaxCalculationService.calculate_total_tax_and_charges(obj.subtotal)
        except ImportError:
            return {
                'cgst': '0.00', 'sgst': '0.00', 'igst': '0.00', 'ugst': '0.00',
                'service_charge': '0.00', 'total_gst': '0.00'
            }

    def get_cgst_amount(self, obj):
        """Get CGST amount"""
        tax_calc = self.get_tax_calculation(obj)
        return str(tax_calc['cgst'])

    def get_sgst_amount(self, obj):
        """Get SGST amount"""
        tax_calc = self.get_tax_calculation(obj)
        return str(tax_calc['sgst'])

    def get_igst_amount(self, obj):
        """Get IGST amount"""
        tax_calc = self.get_tax_calculation(obj)
        return str(tax_calc['igst'])

    def get_ugst_amount(self, obj):
        """Get UGST amount"""
        tax_calc = self.get_tax_calculation(obj)
        return str(tax_calc['ugst'])

    def get_service_charge(self, obj):
        """Get service charge amount"""
        tax_calc = self.get_tax_calculation(obj)
        return str(tax_calc['service_charge'])

    def get_tax_breakdown(self, obj):
        """Get complete tax breakdown with proper GST display logic"""
        tax_calc = self.get_tax_calculation(obj)

        breakdown = []

        # Add CGST and SGST separately if they exist (intra-state)
        if float(tax_calc['cgst']) > 0:
            breakdown.append({
                'type': 'CGST',
                'rate': '9%',  # This should come from stored calculation
                'amount': str(tax_calc['cgst'])
            })

        if float(tax_calc['sgst']) > 0:
            breakdown.append({
                'type': 'SGST',
                'rate': '9%',  # This should come from stored calculation
                'amount': str(tax_calc['sgst'])
            })

        # Add IGST as single entry if it exists (inter-state)
        if float(tax_calc['igst']) > 0:
            breakdown.append({
                'type': 'IGST',
                'rate': '18%',  # This should come from stored calculation
                'amount': str(tax_calc['igst'])
            })

        # Add UGST if it exists (union territory)
        if float(tax_calc['ugst']) > 0:
            breakdown.append({
                'type': 'UGST',
                'rate': '18%',  # This should come from stored calculation
                'amount': str(tax_calc['ugst'])
            })

        # Add service charge
        if float(tax_calc['service_charge']) > 0:
            breakdown.append({
                'type': 'Service Charge',
                'rate': '2.5%',  # This should come from stored calculation
                'amount': str(tax_calc['service_charge'])
            })

        return breakdown


class OrderListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for order listings.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    customer_mobile = serializers.CharField(source='customer.mobile', read_only=True)
    items_count = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer_name', 'customer_mobile',
            'status', 'payment_status', 'total_amount', 'scheduled_date',
            'scheduled_time_slot', 'items_count', 'created_at'
        ]

    def get_items_count(self, obj):
        return obj.items.count()


class CreateOrderSerializer(serializers.Serializer):
    """
    Serializer for creating orders from cart.
    """
    cart_id = serializers.CharField()
    delivery_address = serializers.JSONField()
    payment_method = serializers.ChoiceField(
        choices=[('razorpay', 'Razorpay'), ('cod', 'Cash on Delivery')],
        default='razorpay'
    )
    scheduled_date = serializers.DateField(required=False)
    scheduled_time_slot = serializers.CharField(max_length=50, required=False)
    customer_notes = serializers.CharField(max_length=1000, required=False)

    def validate_delivery_address(self, value):
        """Validate delivery address structure"""
        required_fields = ['house_number', 'street_name', 'city', 'state', 'pincode']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"Missing required field: {field}")
        return value


class OrderStatusUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating order status.
    """
    status = serializers.ChoiceField(choices=Order.STATUS_CHOICES)
    reason = serializers.CharField(max_length=500, required=False)
    admin_notes = serializers.CharField(max_length=1000, required=False)


class OrderCancellationSerializer(serializers.ModelSerializer):
    """
    Serializer for order cancellation.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    cancelled_by_name = serializers.CharField(source='cancelled_by.get_full_name', read_only=True)

    class Meta:
        model = OrderCancellation
        fields = [
            'id', 'order', 'order_number', 'reason', 'description',
            'cancelled_by', 'cancelled_by_name', 'refund_amount',
            'refund_processed', 'cancelled_at'
        ]
        read_only_fields = ['cancelled_at']


class OrderRescheduleSerializer(serializers.ModelSerializer):
    """
    Serializer for order rescheduling.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)

    class Meta:
        model = OrderReschedule
        fields = [
            'id', 'order', 'order_number', 'original_date', 'original_time_slot',
            'new_date', 'new_time_slot', 'reason', 'requested_by', 'requested_by_name',
            'approved', 'approved_by', 'approved_by_name', 'created_at'
        ]
        read_only_fields = ['created_at']


class OrderStatusHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for order status history.
    """
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)

    class Meta:
        model = OrderStatusHistory
        fields = [
            'id', 'previous_status', 'new_status', 'changed_by',
            'changed_by_name', 'reason', 'timestamp'
        ]


class AssignProviderSerializer(serializers.Serializer):
    """
    Serializer for assigning service provider to order.
    """
    provider_id = serializers.IntegerField()
    notes = serializers.CharField(max_length=500, required=False)

    def validate_provider_id(self, value):
        """Validate that provider exists and is active"""
        from authentication.models import User
        try:
            User.objects.get(id=value, user_type='provider', is_active=True)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive service provider.")


class OrderPaymentUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating payment information.
    """
    payment_id = serializers.CharField(max_length=100)
    payment_signature = serializers.CharField(max_length=200, required=False)
    payment_status = serializers.ChoiceField(choices=Order.PAYMENT_STATUS_CHOICES)


class OrderSummarySerializer(serializers.ModelSerializer):
    """
    Minimal serializer for order summaries and dashboards.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer_name', 'status',
            'payment_status', 'total_amount', 'created_at'
        ]


class CustomerOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for customer's own orders (limited fields).
    """
    items = OrderItemSerializer(many=True, read_only=True)
    items_count = serializers.SerializerMethodField()
    can_cancel = serializers.ReadOnlyField(source='can_be_cancelled')
    can_reschedule = serializers.ReadOnlyField(source='can_be_rescheduled')

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'status', 'payment_status', 'payment_method',
            'subtotal', 'tax_amount', 'discount_amount', 'total_amount',
            'coupon_code', 'coupon_discount', 'delivery_address',
            'scheduled_date', 'scheduled_time_slot', 'customer_notes',
            'items', 'items_count', 'can_cancel', 'can_reschedule',
            'created_at', 'updated_at'
        ]

    def get_items_count(self, obj):
        return obj.items.count()


class ProviderOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for service provider's assigned orders.
    """
    items = OrderItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    customer_mobile = serializers.CharField(source='customer.mobile', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer_name', 'customer_mobile',
            'status', 'delivery_address', 'scheduled_date', 'scheduled_time_slot',
            'customer_notes', 'items', 'created_at'
        ]
